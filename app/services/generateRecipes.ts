import {
  Recipe,
  Ingredient,
  DietPreference,
  MealType,
  InstructionType,
  numRecipesPerMealTypeInitialState,
  RecipeInstructions,
} from '@/app/components/types';
import { LlmService } from '@/app/services/LlmService';
import { UnsplashService } from '@/app/services/UnsplashService';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { Roles } from '@/constants/Types';
import { recipeGenerationSchema, recipeBasicsSchema, recipeDetailsSchema } from '@/schemas/recipeGeneration';
import MockedRecipeResponse from '@/constants/mocks/MockedRecipeResponse.json';
import { recipeGenerationModel } from '@/constants/LlmConfigs';

async function getUnsplashImage(query: string): Promise<string> {
  const response = await UnsplashService.getImageUrl(query);

  if (response.success && response.imageUrl) {
    return response.imageUrl;
  }

  // Log the error for debugging but return fallback image
  console.error(`Failed to fetch image for query "${query}":`, response.error);
  return 'https://via.placeholder.com/300x200?text=No+Image';
}

/**
 * Generate basic recipe information (without ingredients and instructions)
 * This is used for the initial recipe list display
 *
 * @returns An array of recipes with basic info and the response ID for future API calls
 */
export async function generateRecipeBasicsAsync(
  ingredients: Ingredient[],
  dietPreferences: DietPreference | null,
  numRecipesPerMealType: Partial<{ [key in MealType]: number }> = numRecipesPerMealTypeInitialState,
  existingRecipeTitles: string[] = []
): Promise<{ recipes: Recipe[]; responseId: string }> {
  // You can set this to true to use mock data without environment variables
  const useMockData = process.env.USE_MOCK_RECIPES === 'true';
  console.log('Using mock data for recipe basics:', useMockData);

  if (useMockData) {
    // For mock data, we'll use the same data but strip out ingredients and instructions
    const mockedRecipes = await Promise.all(
      MockedRecipeResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          title: recipe.title,
          timeInMinutes: recipe.timeInMinutes,
          calories: recipe.calories,
          imageUrl,
          mealType: recipe.mealType,
          // Empty placeholders for ingredients and instructions
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
        };
      })
    );
    // For mock data, return a fake response ID
    return { recipes: mockedRecipes, responseId: 'mock-response-id' };
  }

  const instructions = `You are a professional chef and nutritionist. Generate detailed, practical recipes. Always return a valid JSON object with a "recipes" array.
If user has very limited ingredients, you can use ingredients they don't have. The most important thing is to make recipes that are tailored to their need and realistic. Never sacrifice user preference and nutrition for the sake of using the provided ingredients.
Those are user's dietary preferences: ${JSON.stringify(dietPreferences)}. The recipes you generate should follow this guideline. Don't repeat any existing recipes.
For each recipe, provide:
- A unique ID (format: recipe_title_dietary_patterns. I.e. "Tempeh_Curry_Vegan")
- A title
- Preparation time in minutes (do your best to estimate how long it will take to prepare the whole dish from start to finish)
- Calorie count (per serving)
- A search query for a relevant dish image. Use two words.
- A meal type ${Object.values(MealType).join(', ')}`;

  try {
    let prompt = `Generate x number of recipes for each meal type using these ingredients: ${ingredients.map((ing) => ing.name).join(', ')}.
        Get x and what meal types you should generate from this JSON: ${JSON.stringify(numRecipesPerMealType)},
        Return your response as a JSON object with a "recipes" array.
        `;
    if (existingRecipeTitles.length > 0) {
      prompt += `Don't generate recipes that are already in this list: ${existingRecipeTitles.join(', ')}.`;
    }

    const input: ResponseInputItem[] = [
      {
        role: Roles.developer,
        content: prompt,
      },
    ];

    const response = await LlmService.callLlmApi(
      recipeGenerationModel,
      instructions,
      input,
      1,
      recipeBasicsSchema,
      'recipes'
    );

    const outputText = LlmService.extractOutputText(response);

    if (!outputText) {
      console.error('No response content from LLM API');
      throw new Error('No response from LLM API');
    }

    const parsedResponse = JSON.parse(outputText);

    if (!parsedResponse.recipes || !Array.isArray(parsedResponse.recipes)) {
      console.error('Invalid response format:', parsedResponse);
      return {
        recipes: Object.values(MealType).map((mealType) => ({
          id: 'mock-recipe',
          title: 'Sample Recipe',
          timeInMinutes: 30,
          calories: 600,
          imageUrl: 'https://via.placeholder.com/300x200?text=Sample',
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
          mealType: mealType,
        })),
        responseId: 'error-response-id',
      };
    }

    console.log('Recipe basics generated:', parsedResponse.recipes);

    // Generate images for each recipe in parallel
    const recipesWithImages = await Promise.all(
      parsedResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          ...recipe,
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          imageUrl,
          // Empty placeholders for ingredients and instructions
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
        };
      })
    );

    return {
      recipes: recipesWithImages,
      responseId: response.id,
    };
  } catch (error) {
    console.error('Error generating recipe basics:', error);
    return {
      recipes: [
        {
          id: 'error-recipe',
          title: 'Error Loading Recipes',
          timeInMinutes: 0,
          calories: 0,
          imageUrl: 'https://via.placeholder.com/300x200?text=Error',
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
          mealType: MealType.BREAKFAST,
        },
      ],
      responseId: 'error-response-id',
    };
  }
}

/**
 * Generate detailed recipe information (ingredients and instructions) for a specific recipe
 * This is used when a user clicks on a recipe to view details
 *
 * Uses the previous response ID to maintain context, so the model already has access to
 * the user's ingredients and dietary preferences from the previous conversation.
 *
 * @param recipeId The ID of the recipe to get details for
 * @param recipeTitle The title of the recipe
 * @param mealType The meal type of the recipe
 * @param previousResponseId The ID of the previous response that generated the basic recipe info
 * @returns The ingredients and instructions for the recipe
 */
export async function generateRecipeDetailsAsync(
  recipeId: string,
  recipeTitle: string,
  mealType: MealType,
  previousResponseId: string
): Promise<{ ingredients: Ingredient[]; instructions: RecipeInstructions }> {
  // You can set this to true to use mock data without environment variables
  const useMockData = process.env.USE_MOCK_RECIPES === 'true';
  console.log('Using mock data for recipe details:', useMockData);

  if (useMockData) {
    // Find the recipe in the mock data
    const mockRecipe = MockedRecipeResponse.recipes.find(
      (recipe: any) => recipe.id === recipeId || recipe.title === recipeTitle
    );

    if (mockRecipe) {
      return {
        ingredients: mockRecipe.ingredients,
        instructions: {
          [InstructionType.HIGH_LEVEL]:
            mockRecipe.instructions['High level'] || 'No high-level instructions available.',
          [InstructionType.DETAILED]: mockRecipe.instructions['Detailed'] || 'No detailed instructions available.',
          [InstructionType.TEACH_MODE]:
            mockRecipe.instructions['Teach mode'] || 'No teach mode instructions available.',
        },
      };
    }

    // If not found, return default mock data
    return {
      ingredients: [
        { name: 'Mock ingredient 1', available: true },
        { name: 'Mock ingredient 2', available: false },
      ],
      instructions: {
        [InstructionType.HIGH_LEVEL]: 'Mock high-level instructions',
        [InstructionType.DETAILED]: 'Mock detailed instructions',
        [InstructionType.TEACH_MODE]: 'Mock teach mode instructions',
      },
    };
  }

  const instructions = `You are a professional chef and nutritionist. Generate all ingredients needed and instructions for the recipe "${recipeTitle}" (${mealType}).
You already have information about the user's available ingredients and dietary preferences from our previous conversation.

Provide:
1. A list of ingredients with availability (true if it's in the user's ingredients list, false otherwise)
2. Three versions of step-by-step instructions:
   - High Level: Brief, concise instructions for experienced cooks
   - Detailed: Comprehensive instructions with more explanation. Each step should be on a separate line.
   - Teach Mode: Extremely detailed instructions with explanations of techniques, tips, and the science behind cooking steps. Each step should be on a separate line.

Return your response as a JSON object with "ingredients" and "instructions" properties.`;

  try {
    const input: ResponseInputItem[] = [
      {
        role: Roles.developer,
        content: `Generate detailed ingredients and instructions for the recipe "${recipeTitle}" (${mealType}).`,
      },
    ];

    // Use the previous response ID to maintain context
    const response = await LlmService.callLlmApi(
      recipeGenerationModel,
      instructions,
      input,
      1,
      recipeDetailsSchema,
      'recipeDetails',
      previousResponseId
    );

    const outputText = LlmService.extractOutputText(response);

    if (!outputText) {
      console.error('No response content from LLM API');
      throw new Error('No response from LLM API');
    }

    const parsedResponse = JSON.parse(outputText);

    if (!parsedResponse.ingredients || !parsedResponse.instructions) {
      console.error('Invalid response format:', parsedResponse);
      return {
        ingredients: [{ name: 'Error loading ingredients', available: false }],
        instructions: {
          [InstructionType.HIGH_LEVEL]: 'There was an error generating instructions. Please try again later.',
          [InstructionType.DETAILED]: 'There was an error generating instructions. Please try again later.',
          [InstructionType.TEACH_MODE]: 'There was an error generating instructions. Please try again later.',
        },
      };
    }

    console.log('Recipe details generated for:', recipeTitle);

    return {
      ingredients: parsedResponse.ingredients,
      instructions: {
        [InstructionType.HIGH_LEVEL]:
          parsedResponse.instructions['High level'] || 'No high-level instructions available.',
        [InstructionType.DETAILED]: parsedResponse.instructions['Detailed'] || 'No detailed instructions available.',
        [InstructionType.TEACH_MODE]:
          parsedResponse.instructions['Teach mode'] || 'No teach mode instructions available.',
      },
    };
  } catch (error) {
    console.error('Error generating recipe details:', error);
    return {
      ingredients: [{ name: 'Error loading ingredients', available: false }],
      instructions: {
        [InstructionType.HIGH_LEVEL]: 'There was an error generating instructions. Please try again later.',
        [InstructionType.DETAILED]: 'There was an error generating instructions. Please try again later.',
        [InstructionType.TEACH_MODE]: 'There was an error generating instructions. Please try again later.',
      },
    };
  }
}