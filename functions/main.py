import threading
from firebase_admin import initialize_app, get_app, auth as admin_auth, firestore
from firebase_functions import https_fn, firestore_fn, scheduler_fn
from firebase_functions.https_fn import Request, Response
from openai import OpenAI
import os
import json
import requests
from urllib.parse import quote

# Initialize Firebase Admin SDK (for Firestore/Auth/etc. if needed)
_init_lock = threading.Lock()
_app = None
_db  = None
def ensure_app():
    global _app
    if _app is None:
        with _init_lock:
            if _app is None:
                # If another thread initialised while we waited, get_app() succeeds
                try:
                    _app = get_app()
                except ValueError:              # not initialised yet
                    _app = initialize_app()

def get_db():
    global _db
    ensure_app()
    if _db is None:
        with _init_lock:
            if _db is None:
                _db = firestore.client()
    return _db

@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=300)
def generate(req: Request) -> Response:
    ensure_app()
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    decoded_token = admin_auth.verify_id_token(id_token)
    uid = decoded_token["uid"]

    try:
        data = req.get_json(silent=True)

        if not data or "input" not in data:
            return Response(json.dumps({"error": "Missing 'input' in request body"}), status=400, headers={"Content-Type": "application/json"})

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(json.dumps({"error": "OpenAI API key not found"}), status=500, headers={"Content-Type": "application/json"})

        client = OpenAI(api_key=api_key)

        # Create parameters dictionary with required fields
        params = {
            "model": data.get("model", "gpt-4.1"),
            "input": data["input"],
            "temperature": data.get("temperature", 1),
            "instructions": data.get("instructions"),
            "text": data.get("text", {"format": {"type": "text"}}),
            "user": uid
        }

        # Add previous_response_id if it exists in the request
        if "previous_response_id" in data and data["previous_response_id"]:
            params["previous_response_id"] = data["previous_response_id"]

        response = client.responses.create(**params)

        return Response(json.dumps(response.model_dump()), status=200, headers={"Content-Type": "application/json"})

    except Exception as e:
        return Response(json.dumps({"error": str(e)}), status=500, headers={"Content-Type": "application/json"})


@https_fn.on_request(secrets=["UNSPLASH_ACCESS_KEY"], timeout_sec=10)
def get_unsplash_image(req: Request) -> Response:
    ensure_app()
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "GET":
        return Response(
            json.dumps({"error": "Method not allowed. Only GET requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        query = req.args.get("query")

        if not query:
            return Response(
                json.dumps({"error": "Missing 'query' parameter"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Unsplash API key from secrets
        unsplash_key = os.environ.get("UNSPLASH_ACCESS_KEY")
        if not unsplash_key:
            return Response(
                json.dumps({"error": "Unsplash API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Call Unsplash API
        url = f"https://api.unsplash.com/search/photos?query={quote(query)}&per_page=1"
        headers = {
            "Authorization": f"Client-ID {unsplash_key}",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            return Response(
                json.dumps({
                    "error": f"Unsplash API returned status {response.status_code}",
                    "details": response.text
                }),
                status=response.status_code,
                headers={"Content-Type": "application/json"}
            )

        data = response.json()
        results = data.get("results", [])

        if not results:
            return Response(
                json.dumps({"error": "No images found for the given query"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        image_url = results[0].get("urls", {}).get("regular")

        if not image_url:
            return Response(
                json.dumps({"error": "Image URL not found in API response"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        return Response(
            json.dumps({"imageUrl": image_url}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except requests.exceptions.Timeout:
        return Response(
            json.dumps({"error": "Request to Unsplash API timed out"}),
            status=408,
            headers={"Content-Type": "application/json"}
        )
    except requests.exceptions.RequestException as e:
        return Response(
            json.dumps({"error": f"Network error: {str(e)}"}),
            status=503,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


def get_existing_recipe_ids(uid: str) -> list:
    """
    Retrieve existing recipe IDs for a user from Firestore.

    Args:
        uid: User's unique identifier

    Returns:
        list: List of existing recipe IDs, empty list if no recipes found
    """
    db = get_db()
    recipes_ref = db.collection('recipes').document(uid)
    recipes_doc = recipes_ref.get()

    if not recipes_doc.exists:
        return []

    recipes_data = recipes_doc.to_dict()
    return recipes_data.get("existingRecipeIds", [])


def get_user_diet_preferences(uid: str) -> dict:
    """
    Retrieve user's diet preferences from Firestore.

    Args:
        uid: User's unique identifier

    Returns:
        dict: User's diet preferences

    Raises:
        ValueError: If diet preferences not found
    """
    db = get_db()
    diet_prefs_ref = db.collection('dietPreferences').document(uid)
    diet_prefs_doc = diet_prefs_ref.get()

    if not diet_prefs_doc.exists:
        raise ValueError("Diet preferences not found. Please complete onboarding first.")

    return diet_prefs_doc.to_dict()


def generate_recipes_openai(diet_preferences: dict, uid: str, numRecipes: int = 30, existing_recipe_ids: list = None) -> list:
    """
    Generate recipes using OpenAI API based on user's diet preferences.

    Args:
        diet_preferences: User's dietary preferences and restrictions
        uid: User's unique identifier for API tracking
        numRecipes: Number of recipes to generate (default: 30)
        existing_recipe_ids: List of existing recipe IDs to avoid duplicating (optional)

    Returns:
        list: Generated recipes

    Raises:
        ValueError: If OpenAI API key not found or API call fails
        json.JSONDecodeError: If response parsing fails
    """
    # Get OpenAI API key
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not found")

    client = OpenAI(api_key=api_key)

    # Define the recipe generation schema
    # !!!! Keep this in sync with the TypeScript schema in app/schemas/recipeGeneration.ts !!!!
    recipe_generation_schema = {
        "type": "object",
        "properties": {
            "recipes": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "title": {"type": "string"},
                        "timeInMinutes": {"type": "number"},
                        "calories": {"type": "number"},
                        "ingredients": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "available": {"type": "boolean"}
                                },
                                "required": ["name", "available"],
                                "additionalProperties": False
                            }
                        },
                        "instructions": {
                            "type": "object",
                            "properties": {
                                "High level": {"type": "string"},
                                "Detailed": {"type": "string"},
                                "Teach mode": {"type": "string"}
                            },
                            "required": ["High level", "Detailed", "Teach mode"],
                            "additionalProperties": False
                        },
                        "imageQuery": {"type": "string"},
                        "mealType": {
                            "type": "string",
                            "enum": ["Breakfast", "Lunch", "Dinner", "Snacks", "Dessert"]
                        }
                    },
                    "required": ["id", "title", "timeInMinutes", "calories", "ingredients", "instructions", "imageQuery", "mealType"],
                    "additionalProperties": False
                }
            }
        },
        "required": ["recipes"],
        "additionalProperties": False
    }

    # Create instructions for recipe generation
    existing_recipes_instruction = ""
    if existing_recipe_ids and len(existing_recipe_ids) > 0:
        existing_recipes_instruction = f"""
IMPORTANT: The user already has these recipe IDs in their collection: {existing_recipe_ids}
Please generate completely NEW and DIFFERENT recipes that are not similar to these existing ones.
Avoid similar ingredients, cooking methods, or flavor profiles to ensure variety and prevent duplicates."""

    instructions = f"""You are a professional chef and nutritionist. Generate detailed, practical recipes based on the user's dietary preferences.

User's dietary preferences: {json.dumps(diet_preferences)}
{existing_recipes_instruction}

Generate exactly {numRecipes} recipes:
- {numRecipes // 3} Breakfast recipes
- {numRecipes // 3} Lunch recipes
- {numRecipes // 3} Dinner recipes

For each recipe, provide:
- A unique ID (format: short_title_description_compatible_diets. I.e. "Tempeh_Vegetable_Coconut_Curry_Vegan_GlutenFree")
- A descriptive title
- Preparation time in minutes (realistic estimate from start to finish)
- Calorie count per serving
- Complete list of ingredients with availability (set all to false for now)
- Three versions of step-by-step instructions:
  1. High Level: Brief, concise instructions for experienced cooks
  2. Detailed: Comprehensive instructions with more explanation
  3. Teach Mode: Extremely detailed instructions with explanations of techniques, tips, and cooking science
- A search query for a relevant dish image (use two descriptive words)
- Appropriate meal type

Ensure recipes align with the user's dietary restrictions, preferences, and goals. Make recipes diverse, nutritious, and practical."""

    # Create the prompt
    prompt = f"""Generate {numRecipes} diverse and delicious recipes based on my dietary preferences.
    I want exactly {numRecipes // 3} breakfast recipes, {numRecipes // 3} lunch recipes, and {numRecipes // 3} dinner recipes.
    Make sure each recipe is complete with all required fields and follows my dietary restrictions."""

    # Prepare the API call parameters
    params = {
        "model": "gpt-4.1",
        "input": [{"role": "developer", "content": prompt}],
        "temperature": 0.8,
        "instructions": instructions,
        "text": {
            "format": {
                "name": "recipes",
                "schema": recipe_generation_schema,
                "type": "json_schema"
            }
        },
        "user": uid
    }

    response = client.responses.create(**params)
    output_content = response.output_text

    if not output_content:
        raise ValueError("No response content from OpenAI API")

    # Parse the JSON response
    try:
        parsed_response = json.loads(output_content)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Failed to parse OpenAI response: {str(e)}", output_content, 0)

    if not parsed_response.get("recipes") or not isinstance(parsed_response["recipes"], list):
        raise ValueError("Invalid response format from OpenAI")

    recipes = parsed_response["recipes"]

    return recipes


def store_recipes(uid: str, recipes: list) -> None:
    """
    Store generated recipes in Firestore.
    If the document already exists, append new recipes to the beginning of the existing recipes list
    and update the timestamp and count. Otherwise, create a new document.

    Args:
        uid: User's unique identifier
        recipes: List of generated recipes

    Raises:
        Exception: If Firestore operation fails
    """
    db = get_db()
    recipes_ref = db.collection('recipes').document(uid)

    # Check if document already exists
    existing_doc = recipes_ref.get()

    if existing_doc.exists:
        # Document exists, append new recipes to the beginning of existing list
        existing_data = existing_doc.to_dict()
        existing_recipes = existing_data.get("recipes", [])

        # Prepend new recipes to existing ones
        updated_recipes = recipes + existing_recipes

        # Extract all recipe IDs for the existingRecipeIds field
        all_recipe_ids = [recipe.get("id") for recipe in updated_recipes if recipe.get("id")]

        recipes_data = {
            "recipes": updated_recipes,
            "count": len(updated_recipes),
            "existingRecipeIds": all_recipe_ids,
            "generatedAt": existing_data.get("generatedAt"),
            "lastUpdated": firestore.SERVER_TIMESTAMP,
        }

        # Update the existing document
        recipes_ref.set(recipes_data)
    else:
        # Document doesn't exist, create new one
        # Extract recipe IDs for the existingRecipeIds field
        recipe_ids = [recipe.get("id") for recipe in recipes if recipe.get("id")]

        recipes_data = {
            "recipes": recipes,
            "count": len(recipes),
            "existingRecipeIds": recipe_ids,
            "generatedAt": firestore.SERVER_TIMESTAMP,
        }

        # Create new document
        recipes_ref.set(recipes_data)


@firestore_fn.on_document_created(document="dietPreferences/{uid}", secrets=["OPENAI_API_KEY"], timeout_sec=500)
def generate_recipes_on_diet_preferences_created(event: firestore_fn.Event[firestore_fn.DocumentSnapshot]) -> None:
    """
    Generate 30 recipes (10 breakfast, 10 lunch, 10 dinner) when a new dietPreferences document is created.
    This function is triggered automatically when a user completes onboarding and their diet preferences are saved.
    """
    try:
        # Get the UID from the document path
        uid = event.params["uid"]

        # Get the diet preferences from the created document
        diet_preferences = event.data.to_dict()

        if not diet_preferences:
            print(f"No diet preferences found for user {uid}")
            return

        print(f"Generating recipes for user {uid} with preferences: {diet_preferences}")

        # Step 1: Generate recipes using OpenAI (no existing recipes for new users)
        recipes = generate_recipes_openai(diet_preferences, uid, 3, [])

        # Step 2: Store recipes in Firestore
        store_recipes(uid, recipes)

        print(f"Successfully generated and stored {len(recipes)} recipes for user {uid}")

    except Exception as e:
        print(f"Error generating recipes for user {event.params.get('uid', 'unknown')}: {str(e)}")
        # Don't raise the exception to avoid retries for this background function


@scheduler_fn.on_schedule(schedule="0 0 * * *", timezone="UTC", secrets=["OPENAI_API_KEY"], timeout_sec=600)
def generate_recipes_daily(_event: scheduler_fn.ScheduledEvent) -> None:
    """
    Scheduled function that runs every 24 hours at midnight UTC to generate fresh recipes
    for all users who have diet preferences stored in Firestore.

    This function:
    1. Queries all dietPreferences documents
    2. For each user, generates 15 new recipes (5 breakfast, 5 lunch, 5 dinner)
    3. Stores the recipes in Firestore, appending to existing recipes

    Schedule: "0 0 * * *" means:
    - 0 minutes past the hour
    - 0 hours (midnight)
    - Every day of the month
    - Every month
    - Every day of the week
    """
    print("Starting daily recipe generation for all users...")

    try:
        # Get all dietPreferences documents
        diet_prefs_collection = get_db().collection('dietPreferences')
        diet_prefs_docs = diet_prefs_collection.stream()

        users_processed = 0
        users_failed = 0
        total_recipes_generated = 0

        for doc in diet_prefs_docs:
            uid = doc.id
            diet_preferences = doc.to_dict()

            try:
                print(f"Generating recipes for user {uid}")

                # Get existing recipe IDs to avoid duplicates
                existing_recipe_ids = get_existing_recipe_ids(uid)
                print(f"User {uid} has {len(existing_recipe_ids)} existing recipes")

                # Generate recipes using OpenAI, passing existing recipe IDs
                recipes = generate_recipes_openai(diet_preferences, uid, 3, existing_recipe_ids)

                # Store recipes in Firestore
                store_recipes(uid, recipes)

                users_processed += 1
                total_recipes_generated += len(recipes)
                print(f"Successfully generated {len(recipes)} recipes for user {uid}")

            except Exception as user_error:
                users_failed += 1
                print(f"Failed to generate recipes for user {uid}: {str(user_error)}")
                # Continue processing other users even if one fails
                continue

        print(f"Daily recipe generation completed. Processed: {users_processed} users, Failed: {users_failed} users, Total recipes generated: {total_recipes_generated}")

    except Exception as e:
        print(f"Error in daily recipe generation: {str(e)}")